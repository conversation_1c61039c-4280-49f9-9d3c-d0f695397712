# Ứng dụng Flask WebUI

Đây là ứng dụng web sử dụng Flask làm backend và JavaScript cho giao diện người dùng.

## Cài đặt

1. Cài đặt Python (khuyến nghị Python 3.8 trở lên)
2. <PERSON><PERSON><PERSON> đặt các thư viện cần thiết:

```
pip install -r requirements.txt
```

## Chạy ứng dụng

```
python app.py
```

<PERSON>u khi chạ<PERSON>, ứng dụng sẽ khả dụng tại địa chỉ: http://127.0.0.1:5000

## Cấu trúc thư mục

- `app.py`: File chính của ứng dụng Flask
- `templates/`: Chứa các file HTML
- `static/`: Chứa các file tĩnh (CSS, JavaScript, hình ảnh)
  - `css/`: File CSS
  - `js/`: File JavaScript 