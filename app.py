from flask import Flask, render_template, request, jsonify, send_file, send_from_directory
import os
import json
import uuid
import threading
import time
from datetime import datetime
import pathlib
import shutil
import subprocess
import platform
import webbrowser

# Import các module chương trình
try:
    import data_scrape_core as shopee_core
except ImportError:
    shopee_core = None
    print("Warning: data_scrape_core not found")

try:
    from raw_data_core import RawDataCore
except ImportError:
    RawDataCore = None
    print("Warning: raw_data_core not found")

try:
    from data_handler_core import DataHandlerCore
except ImportError:
    DataHandlerCore = None
    print("Warning: data_handler_core not found")

app = Flask(__name__)

# Thông tin đăng nhập mặc định
DEFAULT_USERNAME = "princekiix"
DEFAULT_PASSWORD = "Beyondk@2025"

# Lưu trữ thông tin các tác vụ đang chạy
tasks = {}
task_lock = threading.Lock()

# Đường dẫn lưu trữ tạm thời
TEMP_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'temp')
os.makedirs(TEMP_DIR, exist_ok=True)

# Danh sách các thư mục thông dụng
COMMON_FOLDERS = {
    'temp': TEMP_DIR,
    'desktop': os.path.join(os.path.expanduser('~'), 'Desktop'),
    'documents': os.path.join(os.path.expanduser('~'), 'Documents'),
    'downloads': os.path.join(os.path.expanduser('~'), 'Downloads')
}

# Danh sách ổ đĩa (chỉ cho Windows)
def get_drives():
    if platform.system() == 'Windows':
        drives = []
        for letter in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ':
            drive = f"{letter}:\\"
            if os.path.exists(drive):
                drives.append(drive)
        return drives
    return []

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/login', methods=['POST'])
def login():
    data = request.json
    username = data.get('username', DEFAULT_USERNAME)
    password = data.get('password', DEFAULT_PASSWORD)

    try:
        # Gọi hàm đăng nhập từ core để lấy cookies
        if shopee_core:
            shopee_core.get_logged_in_cookies(username, password, headless=True)
            # Kiểm tra xem headers có được tạo không
            headers = shopee_core.get_auth_headers()
        else:
            return jsonify({'success': False, 'message': 'Shopee core module không khả dụng'})
        if headers:
            return jsonify({'success': True, 'message': 'Đăng nhập thành công'})
        else:
            return jsonify({'success': False, 'message': 'Đăng nhập thất bại: Không nhận được headers'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Đăng nhập thất bại: {str(e)}'})

@app.route('/api/list_folders', methods=['POST'])
def list_folders():
    data = request.json
    path = data.get('path', '')

    # Nếu đường dẫn trống, trả về danh sách ổ đĩa (Windows) hoặc thư mục gốc (Unix)
    if not path:
        if platform.system() == 'Windows':
            drives = get_drives()
            return jsonify({
                'success': True,
                'current_path': '',
                'parent_path': None,
                'folders': [{'name': drive, 'path': drive} for drive in drives]
            })
        else:
            path = '/'

    # Xử lý đường dẫn để đảm bảo tính bảo mật
    try:
        path = process_output_path(path)

        # Kiểm tra nếu đường dẫn không tồn tại
        if not os.path.exists(path):
            return jsonify({'success': False, 'message': 'Đường dẫn không tồn tại'})

        # Lấy đường dẫn cha
        parent_path = os.path.dirname(path)
        if path == parent_path:  # Trường hợp ở thư mục gốc
            parent_path = None

        # Danh sách các thư mục con
        folders = []
        for item in os.listdir(path):
            item_path = os.path.join(path, item)
            if os.path.isdir(item_path):
                folders.append({
                    'name': item,
                    'path': item_path
                })

        # Sắp xếp theo tên
        folders.sort(key=lambda x: x['name'].lower())

        return jsonify({
            'success': True,
            'current_path': path,
            'parent_path': parent_path,
            'folders': folders
        })

    except PermissionError:
        return jsonify({'success': False, 'message': 'Không có quyền truy cập thư mục này'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Lỗi: {str(e)}'})

@app.route('/api/check_path', methods=['POST'])
def check_path():
    data = request.json
    path = data.get('path', '')

    if not path:
        return jsonify({'success': False, 'message': 'Đường dẫn không được để trống'})

    try:
        # Xử lý đường dẫn để đảm bảo tính bảo mật
        path = process_output_path(path)

        # Kiểm tra đường dẫn có tồn tại không
        if os.path.exists(path):
            if os.path.isdir(path):
                # Kiểm tra quyền ghi
                test_file = os.path.join(path, '.test_write_permission')
                try:
                    with open(test_file, 'w') as f:
                        f.write('test')
                    os.remove(test_file)
                    return jsonify({'success': True, 'message': 'Đường dẫn hợp lệ và có quyền ghi', 'path': path, 'exists': True, 'writable': True})
                except:
                    return jsonify({'success': True, 'message': 'Đường dẫn tồn tại nhưng không có quyền ghi', 'path': path, 'exists': True, 'writable': False})
            else:
                return jsonify({'success': False, 'message': 'Đường dẫn là một file, không phải thư mục'})
        else:
            # Thử tạo thư mục
            try:
                os.makedirs(path, exist_ok=True)
                # Kiểm tra quyền ghi
                test_file = os.path.join(path, '.test_write_permission')
                with open(test_file, 'w') as f:
                    f.write('test')
                os.remove(test_file)
                return jsonify({'success': True, 'message': 'Đã tạo thư mục thành công', 'path': path, 'exists': True, 'writable': True})
            except:
                return jsonify({'success': False, 'message': 'Không thể tạo thư mục'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'Lỗi: {str(e)}'})

@app.route('/api/open_folder', methods=['POST'])
def open_folder():
    data = request.json
    path = data.get('path', '')

    if not path or not os.path.exists(path):
        return jsonify({'success': False, 'message': 'Đường dẫn không tồn tại'})

    try:
        if platform.system() == "Windows":
            os.startfile(path)
        elif platform.system() == "Darwin":  # macOS
            subprocess.call(["open", path])
        else:  # Linux
            subprocess.call(["xdg-open", path])
        return jsonify({'success': True, 'message': 'Đã mở thư mục'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Không thể mở thư mục: {str(e)}'})

@app.route('/api/create_folder', methods=['POST'])
def create_folder():
    data = request.json
    parent_path = data.get('parent_path', '')
    folder_name = data.get('folder_name', '')

    if not parent_path or not folder_name:
        return jsonify({'success': False, 'message': 'Vui lòng cung cấp đường dẫn cha và tên thư mục'})

    # Xử lý tên thư mục để đảm bảo an toàn
    folder_name = folder_name.strip()
    if not folder_name or '/' in folder_name or '\\' in folder_name or folder_name in ['.', '..']:
        return jsonify({'success': False, 'message': 'Tên thư mục không hợp lệ'})

    try:
        # Xử lý đường dẫn cha
        parent_path = process_output_path(parent_path)

        # Kiểm tra đường dẫn cha có tồn tại không
        if not os.path.exists(parent_path) or not os.path.isdir(parent_path):
            return jsonify({'success': False, 'message': 'Đường dẫn cha không tồn tại'})

        # Tạo đường dẫn thư mục mới
        new_folder_path = os.path.join(parent_path, folder_name)

        # Kiểm tra thư mục đã tồn tại chưa
        if os.path.exists(new_folder_path):
            return jsonify({'success': False, 'message': 'Thư mục đã tồn tại'})

        # Tạo thư mục
        os.makedirs(new_folder_path, exist_ok=True)

        return jsonify({
            'success': True,
            'message': f'Đã tạo thư mục "{folder_name}" thành công',
            'new_folder_path': new_folder_path
        })

    except PermissionError:
        return jsonify({'success': False, 'message': 'Không có quyền tạo thư mục tại vị trí này'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Lỗi khi tạo thư mục: {str(e)}'})

@app.route('/api/start_scraping', methods=['POST'])
def start_scraping():
    data = request.json
    shop_ids = data.get('shop_ids', [])
    output_path = data.get('output_path', '')
    output_filename = data.get('output_filename', f'scraped_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx')
    username = data.get('username', DEFAULT_USERNAME)
    password = data.get('password', DEFAULT_PASSWORD)

    if not shop_ids:
        return jsonify({'success': False, 'message': 'Vui lòng cung cấp ít nhất một ID shop'})

    # Tạo task ID và lưu trữ thông tin
    task_id = str(uuid.uuid4())

    # Xử lý đường dẫn lưu trữ
    if not output_path:
        # Nếu không chọn đường dẫn, sử dụng thư mục temp
        output_path = TEMP_DIR
    else:
        output_path = process_output_path(output_path)

    # Đảm bảo output_path tồn tại
    try:
        os.makedirs(output_path, exist_ok=True)
    except Exception as e:
        return jsonify({'success': False, 'message': f'Không thể tạo thư mục: {str(e)}'})

    # Đường dẫn đầy đủ cho file output
    full_output_path = os.path.join(output_path, output_filename)
    if not full_output_path.endswith('.xlsx'):
        full_output_path += '.xlsx'

    # Khởi tạo thông tin tác vụ
    with task_lock:
        tasks[task_id] = {
            'status': 'running',
            'start_time': time.time(),
            'shop_ids': shop_ids,
            'total_shops': len(shop_ids),
            'completed_shops': 0,
            'total_products': 0,
            'output_file': full_output_path,
            'output_folder': output_path,
            'log_messages': [],
            'shop_statuses': {shop_id: {'status': 'waiting', 'product_count': 0} for shop_id in shop_ids}
        }

    # Bắt đầu thread để xử lý dữ liệu
    thread = threading.Thread(
        target=process_data,
        args=(task_id, username, password, shop_ids, output_path, output_filename)
    )
    thread.daemon = True
    thread.start()

    return jsonify({
        'success': True,
        'task_id': task_id,
        'message': f'Đã bắt đầu xử lý {len(shop_ids)} shop'
    })

def process_output_path(path):
    """Xử lý đường dẫn đầu ra được chọn từ giao diện"""
    # Nếu là thư mục trong danh sách thông dụng
    if path.lower() in COMMON_FOLDERS:
        return COMMON_FOLDERS[path.lower()]

    # Nếu là đường dẫn tương đối, chuyển thành đường dẫn tuyệt đối
    if not os.path.isabs(path):
        path = os.path.join(os.path.dirname(os.path.abspath(__file__)), path)

    # Mở rộng ~ nếu có (home directory)
    path = os.path.expanduser(path)

    # Đảm bảo đường dẫn hợp lệ
    if os.name == 'nt':  # Windows
        # Xử lý ổ đĩa cho Windows
        if path.startswith('/') and len(path) > 2 and path[2] == ':':
            # Chuyển /C:/path thành C:/path
            path = path[1:]
        elif not path[1] == ':':
            # Nếu không có chỉ định ổ đĩa, thêm ổ đĩa hiện tại
            current_drive = os.getcwd()[:2]
            if not path.startswith('/'):
                path = os.path.join(current_drive, path)
            else:
                path = current_drive + path

    # Xử lý các dấu cách đường dẫn
    path = os.path.normpath(path)

    return path

def process_data(task_id, username, password, shop_ids, output_path, output_filename):
    """Hàm xử lý dữ liệu trong thread riêng biệt"""

    def log_callback(message):
        with task_lock:
            if task_id in tasks:
                tasks[task_id]['log_messages'].append(message)

                # Phân tích log message để xác định shop_id và cập nhật trạng thái
                import re
                shop_id_match = re.search(r"Đang lấy dữ liệu từ shop ID: (\d+)", message)
                completed_match = re.search(r"Shop ID (\d+): Hoàn thành với (\d+) sản phẩm", message)
                no_products_match = re.search(r"Shop ID (\d+): Không có sản phẩm nào", message)
                no_products_retry_match = re.search(r"Shop ID (\d+): Không có sản phẩm nào \(đã retry (\d+) lần\)", message)
                error_match = re.search(r"Lỗi khi xử lý shop ID (\d+)", message)

                if shop_id_match:
                    shop_id = shop_id_match.group(1)
                    tasks[task_id]['shop_statuses'][shop_id]['status'] = 'processing'
                elif completed_match:
                    shop_id = completed_match.group(1)
                    product_count = int(completed_match.group(2))
                    tasks[task_id]['shop_statuses'][shop_id]['status'] = 'completed'
                    tasks[task_id]['shop_statuses'][shop_id]['product_count'] = product_count
                    tasks[task_id]['completed_shops'] += 1
                    tasks[task_id]['total_products'] += product_count
                elif no_products_match or no_products_retry_match:
                    if no_products_match:
                        shop_id = no_products_match.group(1)
                    else:
                        shop_id = no_products_retry_match.group(1)
                    tasks[task_id]['shop_statuses'][shop_id]['status'] = 'no_products'
                    tasks[task_id]['completed_shops'] += 1
                elif error_match:
                    shop_id = error_match.group(1)
                    tasks[task_id]['shop_statuses'][shop_id]['status'] = 'error'
                    tasks[task_id]['completed_shops'] += 1

    try:
        full_output_path = os.path.join(output_path, output_filename)
        if not full_output_path.endswith('.xlsx'):
            full_output_path += '.xlsx'

        # Gọi hàm xử lý từ core
        if shopee_core:
            result = shopee_core.fetch_and_save_multiple_shops(
            username,
            password,
            shop_ids,
            output_filename=full_output_path,
            headless=True,
            timeout=30,
            max_retries=3,
                log_callback=log_callback,
                max_workers=8  # Sử dụng 8 luồng đồng thời như trong PyQt app
            )
        else:
            result = False

        # Cập nhật trạng thái tác vụ
        with task_lock:
            if task_id in tasks:
                if result:
                    tasks[task_id]['status'] = 'completed'
                    tasks[task_id]['log_messages'].append(f"📊 Tổng cộng: {tasks[task_id]['total_products']} sản phẩm được lưu vào file Excel")
                else:
                    tasks[task_id]['status'] = 'error'
                    tasks[task_id]['log_messages'].append("❌ Xử lý dữ liệu thất bại")

    except Exception as e:
        with task_lock:
            if task_id in tasks:
                tasks[task_id]['status'] = 'error'
                tasks[task_id]['log_messages'].append(f"❌ Lỗi: {str(e)}")

@app.route('/api/get_task_status/<task_id>', methods=['GET'])
def get_task_status(task_id):
    with task_lock:
        if task_id not in tasks:
            return jsonify({'success': False, 'message': 'Không tìm thấy tác vụ'})

        task = tasks[task_id].copy()

        # Tính thời gian đã chạy
        elapsed_time = time.time() - task['start_time']
        hours, remainder = divmod(elapsed_time, 3600)
        minutes, seconds = divmod(remainder, 60)
        task['elapsed_time'] = f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"

        # Tính phần trăm hoàn thành
        if task['total_shops'] > 0:
            task['progress_percent'] = int((task['completed_shops'] / task['total_shops']) * 100)
        else:
            task['progress_percent'] = 0

        return jsonify({'success': True, 'task': task})

@app.route('/api/get_file/<task_id>', methods=['GET'])
def get_file(task_id):
    with task_lock:
        if task_id not in tasks or 'output_file' not in tasks[task_id]:
            return jsonify({'success': False, 'message': 'Không tìm thấy file'})

        output_file = tasks[task_id]['output_file']

    if not os.path.exists(output_file):
        return jsonify({'success': False, 'message': 'File không tồn tại'})

    return send_file(output_file, as_attachment=True)

@app.route('/api/convert_csv', methods=['POST'])
def convert_csv():
    if 'file' not in request.files:
        return jsonify({'success': False, 'message': 'Không tìm thấy file'})

    csv_file = request.files['file']

    if csv_file.filename == '':
        return jsonify({'success': False, 'message': 'Không có file nào được chọn'})

    if not csv_file.filename.endswith('.csv'):
        return jsonify({'success': False, 'message': 'Chỉ chấp nhận file CSV'})

    # Tạo đường dẫn lưu file tạm thời
    csv_temp_path = os.path.join(TEMP_DIR, f"temp_{uuid.uuid4()}.csv")
    excel_temp_path = csv_temp_path.replace('.csv', '.xlsx')

    # Lưu file CSV
    csv_file.save(csv_temp_path)

    # Tạo task ID
    task_id = str(uuid.uuid4())
    with task_lock:
        tasks[task_id] = {
            'status': 'running',
            'start_time': time.time(),
            'output_file': excel_temp_path,
            'output_folder': os.path.dirname(excel_temp_path),
            'log_messages': ["🔄 Bắt đầu chuyển đổi CSV thành Excel..."]
        }

    # Bắt đầu thread để xử lý chuyển đổi
    thread = threading.Thread(
        target=process_csv_conversion,
        args=(task_id, csv_temp_path, excel_temp_path)
    )
    thread.daemon = True
    thread.start()

    return jsonify({
        'success': True,
        'task_id': task_id,
        'message': 'Đã bắt đầu chuyển đổi CSV thành Excel'
    })

def process_csv_conversion(task_id, csv_path, excel_path):
    """Hàm xử lý chuyển đổi CSV thành Excel trong thread riêng biệt"""
    try:
        import pandas as pd
        from openpyxl import Workbook
        from openpyxl.styles import Font

        # Đọc CSV
        df = pd.read_csv(csv_path)

        # Format theo yêu cầu
        key_col = df['id'].astype(str) + '_' + df['shopID'].astype(str)
        sku_col = df['id']
        id_col = df['id']

        new_df = pd.DataFrame({
            '': key_col,
            ' ': sku_col,
            '  ': id_col
        })

        # Thêm các cột còn lại
        column_mapping = {
            'linkProduct': 'Link sản phẩm',
            'linkShop': 'Link Shop',
            'name': 'Tên sản phẩm',
            'brand': 'Thương hiệu',
            'description': 'Mô tả',
            'timeCreate': 'Ngày tạo',
            'itemID': 'Mã Shop',
            'shopID': 'Mã Sản phẩm',
            'categoryMain': 'Chuyên mục',
            'categoryTree': 'Chuyên mục.1',
            'price': 'Giá hiện tại',
            'priceMin': 'Giá thấp nhất',
            'priceMax': 'Giá cao nhất',
            'discount': 'Giảm giá',
            'stock': 'Tồn kho',
            'weight': 'Cân nặng',
            'image': 'Hình ảnh',
            'cmtCount': 'Số Đánh giá',
            'viewCount': 'Số lượt xem',
            'likedCount': 'Số thích',
            'itemRating': 'Điểm đánh giá',
            'sold_30day': 'Đã bán 30 ngày',
            'sale_30day': 'Doanh số 30 ngày',
            'sold_alltime': 'Đã bán toàn thời gian',
            'sale_alltime': 'Doanh số toàn thời gian',
            'location': 'Vị trí',
            'video': 'Video'
        }

        for col_name, new_name in column_mapping.items():
            if col_name in df.columns:
                new_df[new_name] = df[col_name]

        # Tạo Excel
        wb = Workbook()
        ws = wb.active

        # Ghi headers
        for c_idx, column in enumerate(new_df.columns, 1):
            if c_idx <= 3:
                header_value = ""  # 3 cột đầu trống
            else:
                header_value = column
            ws.cell(row=1, column=c_idx, value=header_value)
            ws.cell(row=1, column=c_idx).font = Font(bold=False)

        # Ghi dữ liệu từ row 2
        for r_idx, (_, row) in enumerate(new_df.iterrows(), 2):
            for c_idx, value in enumerate(row, 1):
                try:
                    ws.cell(row=r_idx, column=c_idx, value=value)
                except:
                    ws.cell(row=r_idx, column=c_idx, value="[Lỗi dữ liệu]")

        # Lưu file Excel
        wb.save(excel_path)

        # Cập nhật trạng thái
        with task_lock:
            if task_id in tasks:
                tasks[task_id]['status'] = 'completed'
                tasks[task_id]['log_messages'].append("✅ Chuyển đổi thành công!")
                tasks[task_id]['log_messages'].append(f"📊 Đã xử lý {len(df)} dòng dữ liệu")

        # Xóa file CSV tạm thời
        try:
            os.remove(csv_path)
        except:
            pass

    except Exception as e:
        with task_lock:
            if task_id in tasks:
                tasks[task_id]['status'] = 'error'
                tasks[task_id]['log_messages'].append(f"❌ Lỗi: {str(e)}")

@app.route('/api/get_default_credentials', methods=['GET'])
def get_default_credentials():
    return jsonify({
        'username': DEFAULT_USERNAME,
        'password': DEFAULT_PASSWORD
    })

@app.route('/api/update_credentials', methods=['POST'])
def update_credentials():
    data = request.json
    username = data.get('username')
    password = data.get('password')

    if not username or not password:
        return jsonify({'success': False, 'message': 'Username và password không được để trống'})

    global DEFAULT_USERNAME, DEFAULT_PASSWORD
    DEFAULT_USERNAME = username
    DEFAULT_PASSWORD = password

    # Cập nhật thông tin đăng nhập và force refresh
    try:
        if shopee_core:
            shopee_core.get_logged_in_cookies(username, password, headless=True, force_refresh=True)
            return jsonify({'success': True, 'message': 'Đã cập nhật thông tin đăng nhập'})
        else:
            return jsonify({'success': False, 'message': 'Shopee core module không khả dụng'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Lỗi khi cập nhật thông tin đăng nhập: {str(e)}'})

# ===== RAW DATA PROCESSING ENDPOINTS =====

@app.route('/api/raw_data/get_sheets', methods=['POST'])
def raw_data_get_sheets():
    """Lấy danh sách sheets từ Google Spreadsheet"""
    if not DataHandlerCore:
        return jsonify({'success': False, 'message': 'Data Handler Core module không khả dụng'})

    data = request.json
    spreadsheet_url = data.get('spreadsheet_url', '')

    if not spreadsheet_url:
        return jsonify({'success': False, 'message': 'Vui lòng cung cấp URL spreadsheet'})

    try:
        print(f"🔍 Đang xử lý URL: {spreadsheet_url}")

        # Initialize DataHandlerCore
        data_handler = DataHandlerCore()

        # Extract spreadsheet ID from URL
        spreadsheet_id = data_handler.extract_spreadsheet_id(spreadsheet_url)
        print(f"📋 Spreadsheet ID: {spreadsheet_id}")

        if not spreadsheet_id:
            return jsonify({'success': False, 'message': 'URL spreadsheet không hợp lệ'})

        # Get sheets list
        sheet_list = data_handler.get_sheet_list(spreadsheet_id)
        print(f"📄 Tìm thấy {len(sheet_list)} sheets: {sheet_list}")

        return jsonify({
            'success': True,
            'spreadsheet_id': spreadsheet_id,
            'sheets': sheet_list
        })

    except Exception as e:
        print(f"❌ Lỗi: {str(e)}")
        return jsonify({'success': False, 'message': f'Lỗi khi lấy danh sách sheets: {str(e)}'})

@app.route('/api/raw_data/import', methods=['POST'])
def raw_data_import():
    """Import dữ liệu từ sheets nguồn sang sheet đích"""
    if not DataHandlerCore:
        return jsonify({'success': False, 'message': 'Data Handler Core module không khả dụng'})

    data = request.json
    source_spreadsheet_id = data.get('source_spreadsheet_id', '')
    target_spreadsheet_id = data.get('target_spreadsheet_id', '')
    selected_sheets = data.get('selected_sheets', [])
    target_sheet = data.get('target_sheet', 'Pool Deal')
    mode = data.get('mode', 'copy')

    if not source_spreadsheet_id or not selected_sheets:
        return jsonify({'success': False, 'message': 'Vui lòng cung cấp thông tin nguồn và chọn ít nhất một sheet'})

    if not target_spreadsheet_id:
        target_spreadsheet_id = source_spreadsheet_id

    try:
        print(f"🔄 Bắt đầu import dữ liệu với chế độ '{mode}'")
        print(f"📊 Source: {source_spreadsheet_id}")
        print(f"🎯 Target: {target_spreadsheet_id}")
        print(f"📁 Sheets: {selected_sheets}")
        print(f"📋 Target sheet: {target_sheet}")

        # Initialize DataHandlerCore
        data_handler = DataHandlerCore()

        # Chuẩn bị source data theo format mới
        source_data = []
        for sheet_name in selected_sheets:
            source_data.append({
                'spreadsheet_id': source_spreadsheet_id,
                'sheet_name': sheet_name,
                'start_row': 3,  # Bắt đầu từ dòng 3 (bỏ qua header)
                'end_row': None  # Lấy đến hết
            })

        # Tạo column mapping tự động từ sheet đích
        column_mapping = data_handler.create_column_mapping_from_target_sheet(
            target_spreadsheet_id,
            target_sheet,
            data_handler.remove_vietnamese_accents
        )

        # Thực hiện import dữ liệu
        result = data_handler.process_data_import(
            target_spreadsheet_id,
            source_data,
            target_sheet,
            column_mapping,
            data_handler.remove_vietnamese_accents,
            mode
        )

        if result:
            return jsonify({
                'success': True,
                'message': f'Import dữ liệu thành công với chế độ {mode}'
            })
        else:
            return jsonify({'success': False, 'message': 'Import dữ liệu thất bại'})

    except Exception as e:
        print(f"❌ Lỗi import: {str(e)}")
        return jsonify({'success': False, 'message': f'Lỗi khi import dữ liệu: {str(e)}'})

@app.route('/api/raw_data/process', methods=['POST'])
def raw_data_process():
    """Xử lý dữ liệu đã import"""
    if not DataHandlerCore:
        return jsonify({'success': False, 'message': 'Data Handler Core module không khả dụng'})

    data = request.json
    spreadsheet_id = data.get('spreadsheet_id', '')
    sheet_name = data.get('sheet_name', 'Pool Deal')
    rules = data.get('rules', [])

    if not spreadsheet_id:
        return jsonify({'success': False, 'message': 'Vui lòng cung cấp ID spreadsheet'})

    try:
        print(f"🔧 Bắt đầu xử lý dữ liệu trong sheet '{sheet_name}'")

        # Initialize DataHandlerCore
        data_handler = DataHandlerCore()

        # Nếu không có rules được cung cấp, sử dụng rules mặc định
        if not rules:
            rules = [
                {
                    'condition': 'Clear X Values',
                    'headers': ['KOL Pick', 'Review/ATC']
                },
                {
                    'condition': 'Format Numbers',
                    'headers': ['Giá hiện tại', 'Giá thấp nhất', 'Giá cao nhất']
                },
                {
                    'condition': 'Format Percentages',
                    'headers': ['Giảm giá']
                }
            ]

        # Process data with rules
        result = data_handler.process_imported_data_with_rules(spreadsheet_id, sheet_name, rules)

        if result:
            return jsonify({
                'success': True,
                'message': 'Xử lý dữ liệu thành công'
            })
        else:
            return jsonify({'success': False, 'message': 'Xử lý dữ liệu thất bại'})

    except Exception as e:
        print(f"❌ Lỗi xử lý: {str(e)}")
        return jsonify({'success': False, 'message': f'Lỗi khi xử lý dữ liệu: {str(e)}'})

@app.route('/api/raw_data/get_headers', methods=['POST'])
def raw_data_get_headers():
    """Lấy danh sách headers từ sheet cụ thể"""
    if not DataHandlerCore:
        return jsonify({'success': False, 'message': 'Data Handler Core module không khả dụng'})

    data = request.json
    spreadsheet_id = data.get('spreadsheet_id', '')
    sheet_name = data.get('sheet_name', '')

    if not spreadsheet_id or not sheet_name:
        return jsonify({'success': False, 'message': 'Vui lòng cung cấp spreadsheet ID và tên sheet'})

    try:
        print(f"🔍 Lấy headers từ sheet '{sheet_name}' trong spreadsheet: {spreadsheet_id}")

        # Initialize DataHandlerCore
        data_handler = DataHandlerCore()

        # Lấy headers từ dòng 3 (như Desktop App)
        headers = data_handler.get_sheet_headers(spreadsheet_id, sheet_name, header_row=3)

        if headers:
            # Bỏ qua 3 header đầu tiên (như Desktop App)
            all_headers = headers[3:] if len(headers) > 3 else headers

            # Danh sách các headers cần loại bỏ (như Desktop App)
            excluded_headers = [
                "Cluster",
                "L1 Cat",
                "Nhà bán hàng đồng ý cho BEYONDK mượn sản phẩm chính + sản phẩm quà để KOL review trong Livestream?",
                "KOL có được sử dụng sản phẩm Nhà bán hàng cho mượn trong quá trình diễn ra Livestream với mục đích review sản phẩm này không?",
                "Link hình ảnh sản phẩm (hình sp chính & hình quà chính xác)",
                "(Đối với ngành FMCG) Ngày hết hạn sử dụng của sản phẩm trong phiên live (nếu có)",
                "Cho phép Shopee, Beyond-K, Diệp Lê và các KOL tham gia phiên Livestream được quyền công bố giá sau cùng*trên các nền tảng mạng xã hội cho mục đích truyền thông về phiên Livestream?\n*Giá sau cùng: là giá sản phẩm sau khi đã áp dụng: mã giảm giá của Nhà Bán Hàng (nếu có), trợ giá của Shopee (nếu có) và tất cả mã giảm giá của từ Shopee.",
                "Cho phép Shopee, Beyond-K, Diệp Lê và các KOL tham gia phiên Livestream được sử dụng hình ảnh/thông tin sản phẩm vào trong teaser video/teaser post cho mục đích truyền thông về phiên Livestream ?",
                "Brand note (nếu cần)",
                "Brand note",
                "brand note (nếu cần)",
                "brand note",
                "BRAND NOTE (NẾU CẦN)",
                "BRAND NOTE"
            ]

            # Lọc bỏ các headers không mong muốn
            filtered_headers = []
            for header in all_headers:
                should_exclude = False
                header_clean = header.strip().lower() if header else ""

                for excluded in excluded_headers:
                    excluded_clean = excluded.strip().lower() if excluded else ""

                    # So sánh chính xác
                    if header_clean == excluded_clean:
                        should_exclude = True
                        break

                    # Kiểm tra Brand note
                    if "brand note" in header_clean and "brand note" in excluded_clean:
                        should_exclude = True
                        break

                # Kiểm tra riêng cho tất cả các biến thể của Brand note
                if "brand note" in header_clean:
                    should_exclude = True

                if not should_exclude:
                    filtered_headers.append(header)

            excluded_count = len(all_headers) - len(filtered_headers)
            print(f"📋 Đã lấy {len(filtered_headers)} headers (bỏ qua 3 header đầu + {excluded_count} headers không cần thiết)")

            return jsonify({
                'success': True,
                'headers': filtered_headers,
                'total_headers': len(headers),
                'filtered_headers': len(filtered_headers)
            })
        else:
            return jsonify({'success': False, 'message': 'Không tìm thấy headers trong sheet này'})

    except Exception as e:
        print(f"❌ Lỗi khi lấy headers: {str(e)}")
        return jsonify({'success': False, 'message': f'Lỗi khi lấy headers: {str(e)}'})

def open_browser():
    """Mở trình duyệt sau khi server đã khởi động"""
    time.sleep(1.5)  # Đợi server khởi động hoàn tất
    webbrowser.open('http://127.0.0.1:5000')

if __name__ == '__main__':
    # Khởi động thread để mở trình duyệt
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()

    # Khởi động Flask server
    print("🚀 Đang khởi động server...")
    print("🌐 Server sẽ chạy tại: http://127.0.0.1:5000")
    print("🔗 Trình duyệt sẽ tự động mở trong giây lát...")
    app.run(debug=True, use_reloader=False)