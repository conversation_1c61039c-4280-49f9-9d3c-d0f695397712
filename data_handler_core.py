import os
import time
import re
import unicodedata
from gsheet_manager import GoogleSheetManager

# Thông tin OAuth2 được mã hóa Base64 (sử dụng từ raw_data_core.py)
OAUTH2_CREDENTIALS = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

# Thông tin về template
TEMPLATE_SPREADSHEET_ID = "1zrpyulMDZGcB7wAH9NXrRMdjOLYw4h3Tsf8n7EvS5oU"
TEMPLATE_SHEET_TITLE = "Pool Deal"
DEFAULT_TARGET_SHEET = "Pool Deal"

class DataHandlerCore:
    def __init__(self):
        """Khởi tạo Data Handler Core với Google Sheets Manager"""
        try:
            # Khởi tạo kết nối đến Google Sheet bằng OAuth2
            self.sheet_manager = GoogleSheetManager(auth_type='oauth', credentials_data=OAUTH2_CREDENTIALS)
            # Khởi tạo các service cần thiết
            self.sheets_service = self.sheet_manager.get_sheets_service()
            self.drive_service = self.sheet_manager.get_drive_service()
            print("✅ DataHandlerCore khởi tạo thành công")
        except Exception as e:
            print(f"❌ Lỗi khởi tạo DataHandlerCore: {str(e)}")
            raise

    def extract_spreadsheet_id(self, url_or_id):
        """Trích xuất spreadsheet ID từ URL hoặc trả về ID nếu đã là ID"""
        if not url_or_id:
            return None

        # Các pattern để trích xuất ID từ URL
        patterns = [
            r'https://docs\.google\.com/spreadsheets/d/([a-zA-Z0-9_-]+)',
            r'https://docs\.google\.com/spreadsheets/d/e/([a-zA-Z0-9_-]+)',
            r'([a-zA-Z0-9_-]{25,})'
        ]

        for pattern in patterns:
            match = re.search(pattern, url_or_id)
            if match:
                return match.group(1)

        # Nếu không match pattern nào, coi như đã là ID
        return url_or_id

    def get_sheet_list(self, spreadsheet_id):
        """Lấy danh sách tên các sheet trong spreadsheet"""
        try:
            # Mở spreadsheet
            spreadsheet = self.sheet_manager.open_by_key(spreadsheet_id)

            # Lấy danh sách worksheets
            worksheets = spreadsheet.worksheets()

            # Trả về danh sách tên sheet
            return [ws.title for ws in worksheets]

        except Exception as e:
            print(f"Lỗi khi lấy danh sách sheets: {str(e)}")
            raise

    def get_sheet_data(self, spreadsheet_id, sheet_name):
        """Lấy dữ liệu từ một sheet cụ thể"""
        try:
            # Mở spreadsheet và sheet
            spreadsheet = self.sheet_manager.open_by_key(spreadsheet_id)
            worksheet = spreadsheet.worksheet(sheet_name)

            # Lấy tất cả dữ liệu
            data = worksheet.get_all_records()

            return data

        except Exception as e:
            print(f"Lỗi khi lấy dữ liệu từ sheet '{sheet_name}': {str(e)}")
            raise

    def get_multiple_sheets_data(self, spreadsheet_id, sheet_names):
        """Lấy dữ liệu từ nhiều sheets"""
        try:
            all_data = {}
            spreadsheet = self.sheet_manager.open_by_key(spreadsheet_id)

            for sheet_name in sheet_names:
                try:
                    worksheet = spreadsheet.worksheet(sheet_name)
                    data = worksheet.get_all_records()
                    all_data[sheet_name] = data
                except Exception as e:
                    print(f"Lỗi khi lấy dữ liệu từ sheet '{sheet_name}': {str(e)}")
                    all_data[sheet_name] = []

            return all_data

        except Exception as e:
            print(f"Lỗi khi lấy dữ liệu từ nhiều sheets: {str(e)}")
            raise

    def create_sheet_from_template(self, target_spreadsheet_id, target_sheet_name):
        """Tạo sheet mới từ template"""
        try:
            # Mở template spreadsheet
            template_spreadsheet = self.sheet_manager.open_by_key(TEMPLATE_SPREADSHEET_ID)
            template_worksheet = template_spreadsheet.worksheet(TEMPLATE_SHEET_TITLE)

            # Lấy dữ liệu template
            template_data = template_worksheet.get_all_values()

            # Mở target spreadsheet
            target_spreadsheet = self.sheet_manager.open_by_key(target_spreadsheet_id)

            # Kiểm tra xem sheet đã tồn tại chưa
            try:
                existing_sheet = target_spreadsheet.worksheet(target_sheet_name)
                # Nếu sheet đã tồn tại, xóa nội dung
                existing_sheet.clear()
                target_worksheet = existing_sheet
            except:
                # Nếu sheet chưa tồn tại, tạo mới
                target_worksheet = target_spreadsheet.add_worksheet(
                    title=target_sheet_name,
                    rows=len(template_data),
                    cols=len(template_data[0]) if template_data else 10
                )

            # Copy dữ liệu template vào target sheet
            if template_data:
                target_worksheet.update('A1', template_data)

            return True

        except Exception as e:
            print(f"Lỗi khi tạo sheet từ template: {str(e)}")
            raise

    def copy_data_to_target_sheet(self, target_spreadsheet_id, target_sheet_name, source_data, mode='copy'):
        """Copy dữ liệu vào target sheet"""
        try:
            # Mở target spreadsheet và sheet
            target_spreadsheet = self.sheet_manager.open_by_key(target_spreadsheet_id)

            # Kiểm tra xem sheet có tồn tại không
            try:
                target_worksheet = target_spreadsheet.worksheet(target_sheet_name)
            except:
                # Nếu sheet không tồn tại, tạo mới từ template
                self.create_sheet_from_template(target_spreadsheet_id, target_sheet_name)
                target_worksheet = target_spreadsheet.worksheet(target_sheet_name)

            if mode == 'copy':
                # Chế độ tạo mới: xóa dữ liệu cũ và copy mới
                target_worksheet.clear()

                # Tạo lại từ template
                self.create_sheet_from_template(target_spreadsheet_id, target_sheet_name)
                target_worksheet = target_spreadsheet.worksheet(target_sheet_name)

            # Chuẩn bị dữ liệu để ghi
            all_rows = []

            # Xử lý dữ liệu từ nhiều sheets
            for sheet_name, sheet_data in source_data.items():
                if not sheet_data:
                    continue

                # Thêm header nếu cần
                if not all_rows and sheet_data:
                    # Lấy header từ dữ liệu đầu tiên
                    headers = list(sheet_data[0].keys()) if sheet_data else []
                    if mode == 'copy':
                        # Nếu là chế độ copy, bỏ qua vì đã có template
                        pass
                    else:
                        # Nếu là chế độ append, có thể cần thêm header
                        pass

                # Thêm dữ liệu
                for row_data in sheet_data:
                    row_values = list(row_data.values())
                    all_rows.append(row_values)

            if all_rows:
                if mode == 'append':
                    # Chế độ bổ sung: thêm vào cuối
                    existing_data = target_worksheet.get_all_values()
                    start_row = len(existing_data) + 1

                    # Ghi dữ liệu từ dòng cuối
                    range_name = f'A{start_row}'
                    target_worksheet.update(range_name, all_rows)
                else:
                    # Chế độ copy: ghi đè từ dòng 2 (sau header)
                    if all_rows:
                        target_worksheet.update('A2', all_rows)

            return True

        except Exception as e:
            print(f"Lỗi khi copy dữ liệu: {str(e)}")
            raise

    def process_data(self, spreadsheet_id, sheet_name, rules=None):
        """Xử lý dữ liệu theo các quy tắc"""
        try:
            # Mở spreadsheet và sheet
            spreadsheet = self.sheet_manager.open_by_key(spreadsheet_id)
            worksheet = spreadsheet.worksheet(sheet_name)

            # Lấy dữ liệu hiện tại
            data = worksheet.get_all_records()

            # Áp dụng các quy tắc xử lý (có thể mở rộng sau)
            if rules:
                # Xử lý theo rules (implement sau nếu cần)
                pass

            # Ví dụ: Sắp xếp dữ liệu theo một cột nào đó
            # processed_data = sorted(data, key=lambda x: x.get('column_name', ''))

            return True

        except Exception as e:
            print(f"Lỗi khi xử lý dữ liệu: {str(e)}")
            raise
