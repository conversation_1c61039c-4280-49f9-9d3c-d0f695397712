import pandas as pd
import os, re, tempfile, csv, base64, json, io, sys
from datetime import datetime
from dateutil.parser import parse
from gspread_dataframe import set_with_dataframe
from google.cloud import storage, bigquery
from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import InstalledAppFlow
from gsheet_manager import GoogleSheetManager

# OAuth credentials chính cho Import Data
CREDENTIALS_BASE64 = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

# OAuth credentials thay thế từ các module khác
ALTERNATIVE_CREDENTIALS = [
    "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
]

# C<PERSON>u hình BigQuery
BQ_PROJECT_ID = "beyondk-live-data"
BQ_BUCKET_NAME = "beyondk-scraped-data"
BQ_DATASET_ID = "scraped_data"
BQ_SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
BQ_CREDENTIALS = "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

# Định nghĩa cấu trúc dữ liệu cho bảng BigQuery
EXPECTED_HEADER = [
    "product_link", "shop_link", "item_name", "brand", "description",
    "created_date", "item_id", "shop_id", "cate_code1", "cate_code2",
    "current_price", "lowest_model_price", "highest_model_price",
    "discount", "stock", "weight", "image_link", "rating_count",
    "view_count", "like_count", "rating", "L30d_order", "L30d_gmv",
    "Lifetime_order", "Lifetime_gmv", "Location"
]

# Định nghĩa kiểu dữ liệu cho các cột
INT_COLUMNS = {
    "item_id", "shop_id", "cate_code1", "cate_code2", "stock", "rating_count",
    "view_count", "like_count", "L30d_order", "Lifetime_order"
}

FLOAT_COLUMNS = {
    "current_price", "lowest_model_price", "highest_model_price", "discount",
    "weight", "rating", "L30d_gmv", "Lifetime_gmv"
}

DATE_COLUMNS = {"created_date"}

# Ánh xạ tiếng Việt
VIETNAMESE_MAPPING = {
    "Link sản phẩm": "product_link",
    "Link Shop": "shop_link",
    "Tên sản phẩm": "item_name",
    "Thương hiệu": "brand",
    "Mô tả": "description",
    "Ngày tạo": "created_date",
    "Mã Sản phẩm": "shop_id",  # swapped
    "Mã Shop": "item_id",      # swapped
    "Chuyên mục": None,        # special handling
    "Giá hiện tại": "current_price",
    "Giá thấp nhất": "lowest_model_price",
    "Giá cao nhất": "highest_model_price",
    "Giảm giá": "discount",
    "Tồn kho": "stock",
    "Cân nặng": "weight",
    "Hình ảnh": "image_link",
    "Số Đánh giá": "rating_count",
    "Số lượt xem": "view_count",
    "Số thích": "like_count",
    "Điểm đánh giá": "rating",
    "Đã bán 30 ngày": "L30d_order",
    "Doanh số 30 ngày": "L30d_gmv",
    "Đã bán toàn thời gian": "Lifetime_order",
    "Doanh số toàn thời gian": "Lifetime_gmv",
    "Vị trí": "Location",
    "Video": None  # drop
}

# Tăng giới hạn kích thước trường CSV để xử lý dữ liệu lớn
try:
    csv.field_size_limit(sys.maxsize)
except OverflowError:
    csv.field_size_limit(2147483647)  # 2^31-1

class ImportDataCore:
    def __init__(self):
        """Khởi tạo Import Data Core với Google Sheets Manager"""
        self.sheet_manager = GoogleSheetManager(
            auth_type='oauth',
            credentials_data=CREDENTIALS_BASE64,
            all_oauth_credentials=ALTERNATIVE_CREDENTIALS
        )

    def extract_sheet_id(self, text: str) -> str:
        """Trích xuất Sheet ID từ URL hoặc trả về text gốc"""
        match = re.search(r'https://docs\.google\.com/spreadsheets/d/([^/]+)', text)
        if match:
            return match.group(1)
        return text

    def get_sheet_list(self, spreadsheet_id: str) -> list:
        """Lấy danh sách worksheets từ spreadsheet"""
        try:
            sh = self.sheet_manager.client.open_by_key(spreadsheet_id)
            return [ws.title for ws in sh.worksheets()]
        except Exception as e:
            print(f"Lỗi khi lấy danh sách sheets: {str(e)}")
            return []

    def read_excel_file(self, file_path: str) -> pd.DataFrame:
        """Đọc file Excel và xử lý cột"""
        try:
            df = pd.read_excel(file_path)

            # Xử lý tên cột: đổi các cột chứa "Unnamed" thành chuỗi rỗng
            df.columns = ["" if "Unnamed" in str(col) else col for col in df.columns]

            # Ép cột thứ 3 (nếu có) sang kiểu chuỗi
            if len(df.columns) > 2:
                df.iloc[:, 2] = df.iloc[:, 2].astype(str)

            return df
        except Exception as e:
            raise Exception(f"Lỗi đọc file Excel: {str(e)}")

    def upload_to_google_sheets(self, spreadsheet_id: str, sheet_name: str, df: pd.DataFrame,
                               mode: str = "overwrite", log_callback=None) -> bool:
        """
        Upload DataFrame lên Google Sheets

        Args:
            spreadsheet_id: ID của spreadsheet
            sheet_name: Tên sheet (hoặc "[Tạo sheet mới]" để tạo mới)
            df: DataFrame cần upload
            mode: "overwrite" hoặc "append"
            log_callback: Hàm callback để log progress

        Returns:
            True nếu thành công, False nếu có lỗi
        """
        try:
            if log_callback:
                log_callback(f"🔄 Bắt đầu upload {df.shape[0]} dòng, {df.shape[1]} cột")

            # Mở spreadsheet
            sh = self.sheet_manager.client.open_by_key(spreadsheet_id)

            # Xử lý sheet name
            if sheet_name == "[Tạo sheet mới]":
                # Tạo sheet mới với tên duy nhất
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                new_sheet_name = f"ImportedData_{timestamp}"
                ws = sh.add_worksheet(title=new_sheet_name, rows="100", cols="20")
                append_mode = False
                if log_callback:
                    log_callback(f"📄 Đã tạo sheet mới: {new_sheet_name}")
            else:
                ws = sh.worksheet(sheet_name)

                # Kiểm tra dữ liệu hiện có
                existing_data = ws.col_values(4)  # Kiểm tra cột D
                append_mode = (mode == "append" and existing_data and any(existing_data))

                if not append_mode:
                    ws.clear()
                    if log_callback:
                        log_callback("🗑️ Đã xóa dữ liệu cũ")

            # Upload theo batch
            chunk_size = 20000  # 20k dòng mỗi batch
            total_rows = df.shape[0]
            num_batches = (total_rows // chunk_size) + (1 if total_rows % chunk_size else 0)

            if log_callback:
                log_callback(f"📦 Chia thành {num_batches} batch để upload")

            for i in range(num_batches):
                if append_mode:
                    last_row = len(existing_data)
                    start_row = last_row + (i * chunk_size) + 1
                    include_header = False
                else:
                    start_row = i * chunk_size + 1
                    include_header = (i == 0)

                chunk_df = df.iloc[i * chunk_size: (i + 1) * chunk_size]

                if log_callback:
                    log_callback(f"📤 Upload batch {i + 1}/{num_batches}: dòng {start_row} đến {start_row + len(chunk_df) - 1}")

                # Upload chunk
                set_with_dataframe(ws, chunk_df, row=start_row, col=1,
                                 include_index=False, include_column_header=include_header)

            # Định dạng cột C thành TEXT
            if log_callback:
                log_callback("🎨 Đang định dạng cột C thành Plain Text...")

            self._format_column_c_as_text(ws, total_rows + 1, log_callback)

            if log_callback:
                log_callback("✅ Upload thành công!")

            return True

        except Exception as e:
            if log_callback:
                log_callback(f"❌ Lỗi upload: {str(e)}")
            return False

    def _format_column_c_as_text(self, worksheet, end_row: int, log_callback=None):
        """Định dạng cột C thành TEXT"""
        try:
            start_row = 1
            max_cells_per_request = 50000
            format_chunk_size = max_cells_per_request
            num_format_chunks = (end_row // format_chunk_size) + (1 if end_row % format_chunk_size else 0)

            for i in range(num_format_chunks):
                chunk_start = start_row + (i * format_chunk_size)
                chunk_end = min(start_row + ((i + 1) * format_chunk_size) - 1, end_row)

                cell_range = f'C{chunk_start}:C{chunk_end}'
                format_json = {"numberFormat": {"type": "TEXT"}}

                if log_callback:
                    log_callback(f"🎨 Định dạng cột C từ dòng {chunk_start} đến {chunk_end}")

                worksheet.format(cell_range, format_json)

        except Exception as e:
            if log_callback:
                log_callback(f"⚠️ Cảnh báo: Không thể định dạng cột C - {str(e)}")

    def upload_to_bigquery(self, df: pd.DataFrame, table_name: str, mode: str = "append",
                          log_callback=None) -> bool:
        """
        Upload DataFrame lên BigQuery

        Args:
            df: DataFrame cần upload
            table_name: Tên bảng BigQuery
            mode: "append" hoặc "replace"
            log_callback: Hàm callback để log progress

        Returns:
            True nếu thành công, False nếu có lỗi
        """
        try:
            if log_callback:
                log_callback(f"🔄 Bắt đầu upload lên BigQuery với mode: {mode}")

            # Khởi tạo BigQuery client
            credentials_info = json.loads(base64.b64decode(BQ_CREDENTIALS).decode('utf-8'))
            credentials = Credentials.from_authorized_user_info(credentials_info, BQ_SCOPES)

            if credentials.expired:
                credentials.refresh(Request())

            client = bigquery.Client(project=BQ_PROJECT_ID, credentials=credentials)

            # Chuẩn bị dữ liệu theo schema BigQuery
            processed_df = self._prepare_data_for_bigquery(df, log_callback)

            # Cấu hình job
            table_id = f"{BQ_PROJECT_ID}.{BQ_DATASET_ID}.{table_name}"
            job_config = bigquery.LoadJobConfig()

            if mode == "replace":
                job_config.write_disposition = bigquery.WriteDisposition.WRITE_TRUNCATE
            else:
                job_config.write_disposition = bigquery.WriteDisposition.WRITE_APPEND

            job_config.autodetect = True

            if log_callback:
                log_callback(f"📤 Upload {len(processed_df)} dòng lên bảng {table_name}")

            # Thực hiện upload
            job = client.load_table_from_dataframe(processed_df, table_id, job_config=job_config)
            job.result()  # Chờ job hoàn thành

            if log_callback:
                log_callback("✅ Upload BigQuery thành công!")

            return True

        except Exception as e:
            if log_callback:
                log_callback(f"❌ Lỗi upload BigQuery: {str(e)}")
            return False

    def _prepare_data_for_bigquery(self, df: pd.DataFrame, log_callback=None) -> pd.DataFrame:
        """Chuẩn bị dữ liệu cho BigQuery theo schema định sẵn"""
        try:
            if log_callback:
                log_callback("🔄 Đang chuẩn bị dữ liệu cho BigQuery...")

            # Tạo DataFrame mới với schema chuẩn
            result_df = pd.DataFrame()

            # Map các cột theo VIETNAMESE_MAPPING
            for vn_col, en_col in VIETNAMESE_MAPPING.items():
                if en_col is None:  # Bỏ qua cột không cần thiết
                    continue

                if vn_col in df.columns:
                    result_df[en_col] = df[vn_col]
                else:
                    result_df[en_col] = None  # Tạo cột trống nếu không có

            # Xử lý kiểu dữ liệu
            for col in result_df.columns:
                if col in INT_COLUMNS:
                    result_df[col] = pd.to_numeric(result_df[col], errors='coerce').fillna(0).astype(int)
                elif col in FLOAT_COLUMNS:
                    result_df[col] = pd.to_numeric(result_df[col], errors='coerce').fillna(0.0)
                elif col in DATE_COLUMNS:
                    result_df[col] = pd.to_datetime(result_df[col], errors='coerce')
                else:
                    result_df[col] = result_df[col].astype(str).fillna("")

            if log_callback:
                log_callback(f"✅ Đã chuẩn bị {len(result_df)} dòng dữ liệu")

            return result_df

        except Exception as e:
            if log_callback:
                log_callback(f"❌ Lỗi chuẩn bị dữ liệu: {str(e)}")
            raise
