from flask import Flask, render_template
import webbrowser
import threading
import time

app = Flask(__name__)

@app.route('/')
def index():
    return render_template('index.html')

def open_browser():
    time.sleep(1)
    webbrowser.open('http://127.0.0.1:5000')

if __name__ == '__main__':
    # Mở browser tự động
    threading.Thread(target=open_browser).start()
    
    print("🚀 Starting Flask app...")
    app.run(debug=True, use_reloader=False)
