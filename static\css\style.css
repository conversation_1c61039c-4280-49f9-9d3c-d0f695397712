body {
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

h2 {
    color: #333;
    margin-bottom: 20px;
}

.app-step {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 25px;
}

.table-container {
    max-height: 400px;
    overflow-y: auto;
    margin-top: 20px;
}

.log-container {
    height: 400px;
    overflow-y: auto;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
    font-family: monospace;
    white-space: pre-wrap;
    margin-top: 20px;
}

.log-entry {
    margin-bottom: 5px;
    line-height: 1.5;
}

.log-entry.info {
    color: #0d6efd;
}

.log-entry.success {
    color: #198754;
}

.log-entry.warning {
    color: #fd7e14;
}

.log-entry.error {
    color: #dc3545;
}

.status-waiting {
    color: #6c757d;
}

.status-processing {
    color: #0dcaf0;
    font-weight: bold;
}

.status-completed {
    color: #198754;
    font-weight: bold;
}

.status-error {
    color: #dc3545;
    font-weight: bold;
}

.status-no-products {
    color: #6c757d;
}

.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Folder browser styles */
#folder_list {
    border: 1px solid #dee2e6;
    border-radius: 5px;
}

.folder-item {
    cursor: pointer;
    transition: background-color 0.2s;
}

.folder-item:hover {
    background-color: #e9ecef;
}

.folder-item i {
    color: #fd7e14;
}

.folder-browser .form-control:disabled {
    background-color: #f8f9fa;
    cursor: not-allowed;
}

/* Import Data specific styles */
#import-data {
    min-height: 500px;
}

#import-data .app-step {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 25px;
}

/* Debug styles for Import Data */
#import-data .card {
    border: 1px solid #dee2e6;
    margin-bottom: 1rem;
}

#import-data .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

/* Ensure tab content is visible */
.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block !important;
}

.tab-pane.show {
    display: block !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .app-step {
        padding: 15px;
    }

    .table-container, .log-container {
        max-height: 300px;
    }
}