/**
 * FORCE TAB RENDERER - GIẢI PHÁP TUYỆT ĐỐI
 *
 * <PERSON><PERSON> chế này FORCE RENDER tất cả tabs bằng cách:
 * 1. Temporarily activate mỗi tab để trigger rendering
 * 2. Force execute tất cả JavaScript initialization
 * 3. Ensure tất cả elements được rendered properly
 * 4. Restore về trạng thái ban đầu
 */

class ForceTabRenderer {
    constructor() {
        this.debug = true;
        this.originalActiveTab = null;
        this.forceRenderComplete = false;
        this.tabsToRender = [];

        this.log('🔥 Force Tab Renderer initialized');
        this.init();
    }

    log(message) {
        if (this.debug) {
            console.log(`[ForceRenderer] ${message}`);
        }
    }

    /**
     * Khởi tạo Force Tab Renderer
     */
    init() {
        // Đợi DOM ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.executeForceRender());
        } else {
            // Delay để đảm bảo tất cả scripts đã load
            setTimeout(() => this.executeForceRender(), 500);
        }
    }

    /**
     * EXECUTE FORCE RENDER - CORE FUNCTION
     */
    async executeForceRender() {
        this.log('🚀 Starting FORCE RENDER process...');

        try {
            // Step 1: Discover all tabs
            this.discoverTabs();

            // Step 2: Save current state
            this.saveCurrentState();

            // Step 3: Force render each tab
            await this.forceRenderAllTabs();

            // Step 4: Restore original state
            this.restoreOriginalState();

            // Step 5: Final initialization
            this.finalInitialization();

            this.forceRenderComplete = true;
            this.log('✅ FORCE RENDER completed successfully!');

        } catch (error) {
            this.log(`❌ FORCE RENDER failed: ${error.message}`);
            console.error(error);
        }
    }

    /**
     * Discover all tabs in the page
     */
    discoverTabs() {
        this.log('🔍 Discovering tabs...');

        // Find all tab panes
        const tabPanes = document.querySelectorAll('.tab-pane');
        const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');

        this.tabsToRender = [];

        tabPanes.forEach(pane => {
            const tabId = pane.id;
            const tabButton = document.querySelector(`[data-bs-target="#${tabId}"]`);

            if (tabButton) {
                this.tabsToRender.push({
                    id: tabId,
                    pane: pane,
                    button: tabButton,
                    originalClasses: pane.className,
                    originalButtonClasses: tabButton.className
                });

                this.log(`📝 Discovered tab: ${tabId}`);
            }
        });

        this.log(`✅ Found ${this.tabsToRender.length} tabs to render`);
    }

    /**
     * Save current active tab state
     */
    saveCurrentState() {
        this.log('💾 Saving current state...');

        const activeTab = document.querySelector('.tab-pane.active');
        const activeButton = document.querySelector('.nav-link.active');

        this.originalActiveTab = {
            pane: activeTab,
            button: activeButton,
            paneId: activeTab ? activeTab.id : null,
            buttonId: activeButton ? activeButton.id : null
        };

        this.log(`💾 Saved active tab: ${this.originalActiveTab.paneId}`);
    }

    /**
     * FORCE RENDER ALL TABS - CORE MAGIC
     */
    async forceRenderAllTabs() {
        this.log('🔥 Starting FORCE RENDER for all tabs...');

        for (let i = 0; i < this.tabsToRender.length; i++) {
            const tab = this.tabsToRender[i];
            await this.forceRenderSingleTab(tab, i);

            // Small delay between tabs
            await this.delay(100);
        }
    }

    /**
     * Force render a single tab
     */
    async forceRenderSingleTab(tab, index) {
        this.log(`🔧 Force rendering tab ${index + 1}/${this.tabsToRender.length}: ${tab.id}`);

        try {
            // Step 1: Deactivate all tabs
            this.deactivateAllTabs();

            // Step 2: Force activate this tab
            this.forceActivateTab(tab);

            // Step 3: Wait for rendering
            await this.delay(200);

            // Step 4: Force trigger events
            this.forceTriggerTabEvents(tab);

            // Step 5: Force execute initialization
            this.forceExecuteInitialization(tab);

            // Step 6: Verify rendering
            this.verifyTabRendering(tab);

            this.log(`✅ Tab ${tab.id} force rendered successfully`);

        } catch (error) {
            this.log(`❌ Failed to force render tab ${tab.id}: ${error.message}`);
        }
    }

    /**
     * Deactivate all tabs
     */
    deactivateAllTabs() {
        // Remove active classes from all tab panes
        document.querySelectorAll('.tab-pane').forEach(pane => {
            pane.classList.remove('active', 'show');
        });

        // Remove active classes from all tab buttons
        document.querySelectorAll('.nav-link').forEach(button => {
            button.classList.remove('active');
            button.setAttribute('aria-selected', 'false');
        });
    }

    /**
     * Force activate a specific tab
     */
    forceActivateTab(tab) {
        this.log(`🎯 Force activating tab: ${tab.id}`);

        // Add force rendering class
        tab.pane.classList.add('force-rendering');

        // ABSOLUTE FORCE SHOW - Multiple approaches
        tab.pane.classList.add('tab-pane', 'fade', 'show', 'active', 'force-show', 'debug-tab-visible');
        tab.pane.style.display = 'block';
        tab.pane.style.visibility = 'visible';
        tab.pane.style.opacity = '1';
        tab.pane.style.position = 'relative';
        tab.pane.style.height = 'auto';
        tab.pane.style.minHeight = '400px';
        tab.pane.style.width = '100%';
        tab.pane.style.zIndex = '1';

        // Force activate the button
        tab.button.classList.add('nav-link', 'active');
        tab.button.setAttribute('aria-selected', 'true');

        // Force trigger Bootstrap tab events
        const showEvent = new Event('show.bs.tab', { bubbles: true });
        const shownEvent = new Event('shown.bs.tab', { bubbles: true });

        tab.button.dispatchEvent(showEvent);

        setTimeout(() => {
            tab.button.dispatchEvent(shownEvent);

            // Remove force rendering class and add rendered class
            tab.pane.classList.remove('force-rendering');
            tab.pane.classList.add('force-rendered');
        }, 50);
    }

    /**
     * Force trigger tab-specific events
     */
    forceTriggerTabEvents(tab) {
        this.log(`⚡ Force triggering events for: ${tab.id}`);

        // Trigger custom events
        const customEvents = [
            'tabInitialized',
            'tabRefresh',
            'tabActivated',
            'forceRender'
        ];

        customEvents.forEach(eventName => {
            const event = new CustomEvent(eventName, {
                detail: { tabId: tab.id, forced: true },
                bubbles: true
            });
            tab.pane.dispatchEvent(event);
        });

        // Force trigger resize events (important for layout)
        window.dispatchEvent(new Event('resize'));
    }

    /**
     * Force execute initialization for specific tabs
     */
    forceExecuteInitialization(tab) {
        this.log(`🔧 Force executing initialization for: ${tab.id}`);

        try {
            // Tab-specific initialization
            switch (tab.id) {
                case 'import-data':
                    this.forceInitImportData();
                    break;
                case 'data-handler':
                    this.forceInitDataHandler();
                    break;
                case 'data-scraping':
                    this.forceInitDataScraping();
                    break;
                case 'more-tools':
                    this.forceInitMoreTools();
                    break;
                default:
                    this.forceInitGeneric(tab);
            }

        } catch (error) {
            this.log(`❌ Error in force initialization for ${tab.id}: ${error.message}`);
        }
    }

    /**
     * Force init Import Data tab
     */
    forceInitImportData() {
        this.log('🔧 Force initializing Import Data...');

        // Force setup upload method toggle
        const radios = document.querySelectorAll('input[name="importUploadMethod"]');
        radios.forEach(radio => {
            radio.addEventListener('change', () => {
                if (typeof toggleImportUploadMethod === 'function') {
                    toggleImportUploadMethod();
                }
            });
        });

        // Force initial toggle
        if (typeof toggleImportUploadMethod === 'function') {
            toggleImportUploadMethod();
        }

        // Force setup other elements
        const elements = {
            loadSheets: document.getElementById('importLoadSheets'),
            uploadBtn: document.getElementById('importUploadBtn')
        };

        if (elements.loadSheets) {
            elements.loadSheets.addEventListener('click', () => {
                if (typeof loadImportSheets === 'function') {
                    loadImportSheets();
                }
            });
        }

        if (elements.uploadBtn) {
            elements.uploadBtn.addEventListener('click', () => {
                if (typeof startImportUpload === 'function') {
                    startImportUpload();
                }
            });
        }
    }

    /**
     * Force init other tabs
     */
    forceInitDataHandler() {
        this.log('🔧 Force initializing Data Handler...');
        // Add specific initialization if needed
    }

    forceInitDataScraping() {
        this.log('🔧 Force initializing Data Scraping...');
        // Add specific initialization if needed
    }

    forceInitMoreTools() {
        this.log('🔧 Force initializing More Tools...');
        // Add specific initialization if needed
    }

    forceInitGeneric(tab) {
        this.log(`🔧 Force initializing generic tab: ${tab.id}`);

        // Generic initialization
        const buttons = tab.pane.querySelectorAll('button');
        const inputs = tab.pane.querySelectorAll('input, select, textarea');

        this.log(`Found ${buttons.length} buttons and ${inputs.length} inputs in ${tab.id}`);
    }

    /**
     * Verify tab rendering
     */
    verifyTabRendering(tab) {
        const isVisible = tab.pane.offsetHeight > 0 && tab.pane.offsetWidth > 0;
        const hasContent = tab.pane.children.length > 0;

        this.log(`🔍 Verification for ${tab.id}: visible=${isVisible}, hasContent=${hasContent}`);

        if (!isVisible || !hasContent) {
            this.log(`⚠️ Tab ${tab.id} may not be properly rendered`);
        }
    }

    /**
     * Restore original active tab
     */
    restoreOriginalState() {
        this.log('🔄 Restoring original state...');

        // Deactivate all tabs first
        this.deactivateAllTabs();

        // Restore original active tab
        if (this.originalActiveTab.pane && this.originalActiveTab.button) {
            this.originalActiveTab.pane.classList.add('show', 'active');
            this.originalActiveTab.button.classList.add('active');
            this.originalActiveTab.button.setAttribute('aria-selected', 'true');

            this.log(`🔄 Restored active tab: ${this.originalActiveTab.paneId}`);
        }
    }

    /**
     * Final initialization after force render
     */
    finalInitialization() {
        this.log('🎯 Running final initialization...');

        // Trigger global events
        window.dispatchEvent(new CustomEvent('forceRenderComplete', {
            detail: { tabsRendered: this.tabsToRender.length }
        }));

        // Final resize trigger
        setTimeout(() => {
            window.dispatchEvent(new Event('resize'));
        }, 100);
    }

    /**
     * Utility: Delay function
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Public method to force re-render a specific tab
     */
    forceRerenderTab(tabId) {
        this.log(`🔄 Force re-rendering tab: ${tabId}`);

        const tab = this.tabsToRender.find(t => t.id === tabId);
        if (tab) {
            this.forceRenderSingleTab(tab, 0);
        } else {
            this.log(`❌ Tab not found: ${tabId}`);
        }
    }

    /**
     * Public method to get render status
     */
    getRenderStatus() {
        return {
            complete: this.forceRenderComplete,
            tabsRendered: this.tabsToRender.length,
            tabs: this.tabsToRender.map(t => t.id)
        };
    }

    /**
     * Debug console methods
     */
    debugShowAllTabs() {
        this.log('🔍 DEBUG: Showing all tabs...');
        this.tabsToRender.forEach(tab => {
            tab.pane.classList.add('debug-tab-visible', 'force-show');
            tab.pane.style.display = 'block';
            tab.pane.style.position = 'relative';
            tab.pane.style.zIndex = '1';
            this.log(`👁️ DEBUG: ${tab.id} forced visible`);
        });
    }

    debugHideAllTabs() {
        this.log('🙈 DEBUG: Hiding all tabs...');
        this.tabsToRender.forEach(tab => {
            tab.pane.classList.remove('debug-tab-visible', 'force-show');
            tab.pane.style.display = '';
            tab.pane.style.position = '';
            tab.pane.style.zIndex = '';
            this.log(`🙈 DEBUG: ${tab.id} hidden`);
        });
    }

    debugTabInfo(tabId) {
        const tab = this.tabsToRender.find(t => t.id === tabId);
        if (tab) {
            const info = {
                id: tab.id,
                classes: tab.pane.className,
                styles: {
                    display: tab.pane.style.display,
                    visibility: tab.pane.style.visibility,
                    opacity: tab.pane.style.opacity,
                    position: tab.pane.style.position,
                    height: tab.pane.style.height,
                    width: tab.pane.style.width
                },
                dimensions: {
                    offsetWidth: tab.pane.offsetWidth,
                    offsetHeight: tab.pane.offsetHeight,
                    clientWidth: tab.pane.clientWidth,
                    clientHeight: tab.pane.clientHeight
                },
                children: tab.pane.children.length,
                isVisible: tab.pane.offsetHeight > 0 && tab.pane.offsetWidth > 0
            };
            console.table(info);
            return info;
        } else {
            this.log(`❌ DEBUG: Tab not found: ${tabId}`);
            return null;
        }
    }
}

// Create global instance
window.ForceTabRenderer = new ForceTabRenderer();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ForceTabRenderer;
}
