/**
 * TAB CONFIGURATIONS - C<PERSON>u hình khởi tạo cho từng tab
 * 
 * File này chứa logic khởi tạo cụ thể cho từng tab
 * Mỗi khi thêm tab mới, chỉ cần thêm cấu hình vào đây
 */

// Đợi TabManager sẵn sàng
document.addEventListener('DOMContentLoaded', function() {
    // Đợi TabManager được khởi tạo
    setTimeout(() => {
        if (window.TabManager) {
            registerAllTabConfigs();
        }
    }, 100);
});

/**
 * Đăng ký tất cả cấu hình tabs
 */
function registerAllTabConfigs() {
    console.log('🔧 Registering all tab configurations...');

    // 1. DATA HANDLER TAB
    window.TabManager.registerTab('data-handler', initDataHandlerTab, {
        description: 'Data Handler - Xử lý dữ liệu thô',
        priority: 1
    });

    // 2. DATA SCRAPING TAB  
    window.TabManager.registerTab('data-scraping', initDataScrapingTab, {
        description: 'Data Scraping - Thu thập dữ liệu từ Shopee',
        priority: 2
    });

    // 3. IMPORT DATA TAB
    window.TabManager.registerTab('import-data', initImportDataTab, {
        description: 'Import Data - Upload file Excel lên Google Sheets/BigQuery',
        priority: 3
    });

    // 4. MORE TOOLS TAB
    window.TabManager.registerTab('more-tools', initMoreToolsTab, {
        description: 'More Tools - Các công cụ bổ sung',
        priority: 4
    });

    console.log('✅ All tab configurations registered');
}

/**
 * KHỞI TẠO DATA HANDLER TAB
 */
function initDataHandlerTab() {
    console.log('🔧 Initializing Data Handler tab...');
    
    try {
        // Kiểm tra elements tồn tại
        const elements = {
            sourceUrl: document.getElementById('rawDataSourceUrl'),
            loadSheets: document.getElementById('rawDataLoadSheets'),
            targetUrl: document.getElementById('rawDataTargetUrl'),
            loadTargetSheets: document.getElementById('rawDataLoadTargetSheets'),
            importBtn: document.getElementById('rawDataImportBtn'),
            processBtn: document.getElementById('rawDataProcessBtn')
        };

        // Kiểm tra elements
        const missingElements = Object.entries(elements)
            .filter(([key, element]) => !element)
            .map(([key]) => key);

        if (missingElements.length > 0) {
            console.warn('⚠️ Data Handler missing elements:', missingElements);
        }

        // Setup event listeners nếu elements tồn tại
        if (elements.loadSheets) {
            elements.loadSheets.addEventListener('click', handleDataHandlerLoadSheets);
        }
        
        if (elements.loadTargetSheets) {
            elements.loadTargetSheets.addEventListener('click', handleDataHandlerLoadTargetSheets);
        }
        
        if (elements.importBtn) {
            elements.importBtn.addEventListener('click', handleDataHandlerImport);
        }

        console.log('✅ Data Handler tab initialized');
        
    } catch (error) {
        console.error('❌ Error initializing Data Handler tab:', error);
    }
}

/**
 * KHỞI TẠO DATA SCRAPING TAB
 */
function initDataScrapingTab() {
    console.log('🔧 Initializing Data Scraping tab...');
    
    try {
        // Kiểm tra elements tồn tại
        const elements = {
            startBtn: document.getElementById('startScraping'),
            stopBtn: document.getElementById('stopScraping'),
            urlInput: document.getElementById('shopeeUrl'),
            spreadsheetUrl: document.getElementById('spreadsheetUrl')
        };

        // Setup event listeners
        Object.entries(elements).forEach(([key, element]) => {
            if (element) {
                console.log(`✅ Data Scraping element found: ${key}`);
            } else {
                console.warn(`⚠️ Data Scraping element missing: ${key}`);
            }
        });

        console.log('✅ Data Scraping tab initialized');
        
    } catch (error) {
        console.error('❌ Error initializing Data Scraping tab:', error);
    }
}

/**
 * KHỞI TẠO IMPORT DATA TAB
 */
function initImportDataTab() {
    console.log('🔧 Initializing Import Data tab...');
    
    try {
        // Kiểm tra elements tồn tại
        const elements = {
            uploadMethodRadios: document.querySelectorAll('input[name="importUploadMethod"]'),
            googleSheetsConfig: document.getElementById('googleSheetsConfig'),
            bigQueryConfig: document.getElementById('bigQueryConfig'),
            loadSheets: document.getElementById('importLoadSheets'),
            uploadBtn: document.getElementById('importUploadBtn'),
            spreadsheetUrl: document.getElementById('importSpreadsheetUrl'),
            sheetSelect: document.getElementById('importSheetSelect'),
            fileInput: document.getElementById('importExcelFile')
        };

        // Log elements status
        Object.entries(elements).forEach(([key, element]) => {
            if (element) {
                console.log(`✅ Import Data element found: ${key}`);
            } else {
                console.warn(`⚠️ Import Data element missing: ${key}`);
            }
        });

        // Setup upload method toggle - QUAN TRỌNG!
        if (elements.uploadMethodRadios.length > 0) {
            elements.uploadMethodRadios.forEach(radio => {
                radio.addEventListener('change', toggleImportUploadMethodSafe);
            });
            
            // Khởi tạo trạng thái ban đầu
            toggleImportUploadMethodSafe();
        }

        // Setup event listeners
        if (elements.loadSheets) {
            elements.loadSheets.addEventListener('click', loadImportSheetsSafe);
        }
        
        if (elements.uploadBtn) {
            elements.uploadBtn.addEventListener('click', startImportUploadSafe);
        }

        console.log('✅ Import Data tab initialized');
        
    } catch (error) {
        console.error('❌ Error initializing Import Data tab:', error);
    }
}

/**
 * KHỞI TẠO MORE TOOLS TAB
 */
function initMoreToolsTab() {
    console.log('🔧 Initializing More Tools tab...');
    
    try {
        // Placeholder cho More Tools
        const moreToolsElement = document.getElementById('more-tools');
        if (moreToolsElement) {
            console.log('✅ More Tools tab found');
        }

        console.log('✅ More Tools tab initialized');
        
    } catch (error) {
        console.error('❌ Error initializing More Tools tab:', error);
    }
}

/**
 * SAFE FUNCTIONS - Các function an toàn cho Import Data
 */

// Safe toggle upload method
function toggleImportUploadMethodSafe() {
    try {
        const googleSheetsConfig = document.getElementById('googleSheetsConfig');
        const bigQueryConfig = document.getElementById('bigQueryConfig');
        const selectedMethodElement = document.querySelector('input[name="importUploadMethod"]:checked');
        
        if (!googleSheetsConfig || !bigQueryConfig || !selectedMethodElement) {
            console.warn('⚠️ Import Data toggle elements not ready');
            return;
        }
        
        const selectedMethod = selectedMethodElement.value;

        if (selectedMethod === 'google_sheets') {
            googleSheetsConfig.style.display = 'block';
            bigQueryConfig.style.display = 'none';
        } else {
            googleSheetsConfig.style.display = 'none';
            bigQueryConfig.style.display = 'block';
        }
        
        console.log(`🔄 Import Data method toggled to: ${selectedMethod}`);
        
    } catch (error) {
        console.error('❌ Error in toggleImportUploadMethodSafe:', error);
    }
}

// Safe load sheets
function loadImportSheetsSafe() {
    try {
        if (typeof loadImportSheets === 'function') {
            loadImportSheets();
        } else {
            console.warn('⚠️ loadImportSheets function not found');
        }
    } catch (error) {
        console.error('❌ Error in loadImportSheetsSafe:', error);
    }
}

// Safe start upload
function startImportUploadSafe() {
    try {
        if (typeof startImportUpload === 'function') {
            startImportUpload();
        } else {
            console.warn('⚠️ startImportUpload function not found');
        }
    } catch (error) {
        console.error('❌ Error in startImportUploadSafe:', error);
    }
}

/**
 * PLACEHOLDER HANDLERS - Sẽ được thay thế bởi logic thực tế
 */

function handleDataHandlerLoadSheets() {
    console.log('🔄 Data Handler: Load sheets clicked');
}

function handleDataHandlerLoadTargetSheets() {
    console.log('🔄 Data Handler: Load target sheets clicked');
}

function handleDataHandlerImport() {
    console.log('🔄 Data Handler: Import clicked');
}
