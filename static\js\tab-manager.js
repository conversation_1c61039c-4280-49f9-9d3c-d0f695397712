/**
 * TAB MANAGER - G<PERSON><PERSON>i pháp triệt để cho Bootstrap Tab Bug
 * 
 * Vấn đề: Tab mới tích hợp chỉ hiển thị khi ở vị trí đầu tiên
 * Nguyên nhân: JavaScript initialization không được gọi đúng cách cho inactive tabs
 * Giải pháp: Tab Manager tự động khởi tạo tất cả tabs bất kể vị trí
 */

class TabManager {
    constructor() {
        this.tabs = new Map();
        this.initialized = false;
        this.debug = true;
        
        this.log('🚀 Tab Manager initialized');
        this.init();
    }

    log(message) {
        if (this.debug) {
            console.log(`[TabManager] ${message}`);
        }
    }

    /**
     * Khởi tạo Tab Manager
     */
    init() {
        // Đợi DOM ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    /**
     * Setup Tab Manager
     */
    setup() {
        this.log('🔧 Setting up Tab Manager...');
        
        // Đăng ký tất cả tabs
        this.registerAllTabs();
        
        // Khởi tạo tất cả tabs ngay lập tức
        this.initializeAllTabs();
        
        // Lắng nghe tab change events
        this.setupTabChangeListeners();
        
        this.initialized = true;
        this.log('✅ Tab Manager setup completed');
    }

    /**
     * Đăng ký tab mới
     */
    registerTab(tabId, initFunction, options = {}) {
        this.log(`📝 Registering tab: ${tabId}`);
        
        this.tabs.set(tabId, {
            id: tabId,
            initFunction: initFunction,
            initialized: false,
            options: options,
            element: null
        });

        // Nếu Tab Manager đã sẵn sàng, khởi tạo tab ngay
        if (this.initialized) {
            this.initializeTab(tabId);
        }
    }

    /**
     * Tự động đăng ký tất cả tabs có sẵn
     */
    registerAllTabs() {
        // Tìm tất cả tab panes
        const tabPanes = document.querySelectorAll('.tab-pane');
        
        tabPanes.forEach(pane => {
            const tabId = pane.id;
            this.log(`🔍 Found tab: ${tabId}`);
            
            // Đăng ký tab với function mặc định
            if (!this.tabs.has(tabId)) {
                this.registerTab(tabId, () => this.defaultTabInit(tabId));
            }
        });
    }

    /**
     * Khởi tạo tất cả tabs
     */
    initializeAllTabs() {
        this.log('🔄 Initializing all tabs...');
        
        this.tabs.forEach((tab, tabId) => {
            this.initializeTab(tabId);
        });
    }

    /**
     * Khởi tạo một tab cụ thể
     */
    initializeTab(tabId) {
        const tab = this.tabs.get(tabId);
        if (!tab) {
            this.log(`❌ Tab not found: ${tabId}`);
            return;
        }

        if (tab.initialized) {
            this.log(`⚠️ Tab already initialized: ${tabId}`);
            return;
        }

        this.log(`🔧 Initializing tab: ${tabId}`);

        try {
            // Lấy tab element
            tab.element = document.getElementById(tabId);
            
            if (!tab.element) {
                this.log(`❌ Tab element not found: ${tabId}`);
                return;
            }

            // Gọi init function
            if (typeof tab.initFunction === 'function') {
                tab.initFunction();
                tab.initialized = true;
                this.log(`✅ Tab initialized successfully: ${tabId}`);
            } else {
                this.log(`⚠️ No init function for tab: ${tabId}`);
            }

        } catch (error) {
            this.log(`❌ Error initializing tab ${tabId}: ${error.message}`);
            console.error(error);
        }
    }

    /**
     * Function khởi tạo mặc định cho tab
     */
    defaultTabInit(tabId) {
        this.log(`🔧 Default initialization for tab: ${tabId}`);
        
        // Kiểm tra và khởi tạo các elements cơ bản
        const tabElement = document.getElementById(tabId);
        if (tabElement) {
            // Đảm bảo tab có thể hiển thị
            tabElement.style.minHeight = '400px';
            
            // Trigger custom event
            const event = new CustomEvent('tabInitialized', {
                detail: { tabId: tabId }
            });
            tabElement.dispatchEvent(event);
        }
    }

    /**
     * Setup tab change listeners
     */
    setupTabChangeListeners() {
        // Lắng nghe Bootstrap tab events
        document.addEventListener('shown.bs.tab', (event) => {
            const targetId = event.target.getAttribute('data-bs-target')?.replace('#', '');
            if (targetId) {
                this.onTabShown(targetId);
            }
        });

        // Lắng nghe tab clicks
        document.addEventListener('click', (event) => {
            if (event.target.matches('[data-bs-toggle="tab"]')) {
                const targetId = event.target.getAttribute('data-bs-target')?.replace('#', '');
                if (targetId) {
                    this.onTabClick(targetId);
                }
            }
        });
    }

    /**
     * Xử lý khi tab được hiển thị
     */
    onTabShown(tabId) {
        this.log(`👁️ Tab shown: ${tabId}`);
        
        // Đảm bảo tab được khởi tạo
        if (!this.isTabInitialized(tabId)) {
            this.initializeTab(tabId);
        }

        // Trigger refresh cho tab
        this.refreshTab(tabId);
    }

    /**
     * Xử lý khi tab được click
     */
    onTabClick(tabId) {
        this.log(`🖱️ Tab clicked: ${tabId}`);
        
        // Pre-initialize tab nếu chưa được khởi tạo
        if (!this.isTabInitialized(tabId)) {
            this.initializeTab(tabId);
        }
    }

    /**
     * Refresh tab
     */
    refreshTab(tabId) {
        const tab = this.tabs.get(tabId);
        if (tab && tab.element) {
            // Trigger custom refresh event
            const event = new CustomEvent('tabRefresh', {
                detail: { tabId: tabId }
            });
            tab.element.dispatchEvent(event);
        }
    }

    /**
     * Kiểm tra tab đã được khởi tạo chưa
     */
    isTabInitialized(tabId) {
        const tab = this.tabs.get(tabId);
        return tab ? tab.initialized : false;
    }

    /**
     * Force re-initialize tab
     */
    reinitializeTab(tabId) {
        const tab = this.tabs.get(tabId);
        if (tab) {
            tab.initialized = false;
            this.initializeTab(tabId);
        }
    }

    /**
     * Get tab info
     */
    getTabInfo(tabId) {
        return this.tabs.get(tabId);
    }

    /**
     * List all tabs
     */
    listTabs() {
        const tabList = Array.from(this.tabs.keys());
        this.log(`📋 Registered tabs: ${tabList.join(', ')}`);
        return tabList;
    }
}

// Tạo instance global
window.TabManager = new TabManager();

// Export cho module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TabManager;
}
