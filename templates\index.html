<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Tool Dashboard</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container-fluid mt-3">
        <div class="row mb-3">
            <div class="col">
                <h2 class="text-center mb-4">
                    <i class="fas fa-tools"></i> Multi-Tool Dashboard
                </h2>
            </div>
        </div>

        <!-- Main Tab Navigation -->
        <ul class="nav nav-tabs nav-justified mb-4" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="data-handler-tab" data-bs-toggle="tab" data-bs-target="#data-handler" type="button" role="tab">
                    <i class="fas fa-cogs"></i> Data Handler
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="data-scraping-tab" data-bs-toggle="tab" data-bs-target="#data-scraping" type="button" role="tab">
                    <i class="fas fa-download"></i> Data Scraping
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="more-tools-tab" data-bs-toggle="tab" data-bs-target="#more-tools" type="button" role="tab">
                    <i class="fas fa-plus"></i> More Tools
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="mainTabContent">
            <!-- Data Handler Tab -->
            <div class="tab-pane fade show active" id="data-handler" role="tabpanel">
                <div class="app-step">
                    <h4 class="mb-4"><i class="fas fa-cogs"></i> Data Handler</h4>

                    <!-- Import Mode Selection -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Chế độ Import</h6>
                                </div>
                                <div class="card-body">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="rawDataMode" id="rawDataCopyMode" value="copy" checked>
                                        <label class="form-check-label" for="rawDataCopyMode">
                                            Tạo Mới
                                        </label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="rawDataMode" id="rawDataAppendMode" value="append">
                                        <label class="form-check-label" for="rawDataAppendMode">
                                            Bổ Sung
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">
                                        Tạo Mới: Tạo mới hoàn toàn dữ liệu từ sheet nguồn. Bổ Sung: Bổ sung dữ liệu từ dòng 'More' trong sheet nguồn vào cuối sheet đích.
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Source Spreadsheet -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Spreadsheet Nguồn</h6>
                                </div>
                                <div class="card-body">
                                    <div class="input-group mb-3">
                                        <input type="text" id="rawDataSourceUrl" class="form-control" placeholder="Nhập URL hoặc ID Spreadsheet nguồn">
                                        <button id="rawDataLoadSheets" class="btn btn-outline-primary" type="button">
                                            <i class="fas fa-download"></i> Load Sheets
                                        </button>
                                    </div>
                                    <div id="rawDataSheetList" class="row">
                                        <!-- Sheet checkboxes sẽ được load ở đây -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Target Spreadsheet -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Spreadsheet Đích</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="input-group mb-3">
                                                <input type="text" id="rawDataTargetUrl" class="form-control" placeholder="Nhập URL hoặc ID Spreadsheet đích (để trống = dùng nguồn)">
                                                <button id="rawDataLoadTargetSheets" class="btn btn-outline-secondary" type="button">
                                                    <i class="fas fa-sync"></i> Load
                                                </button>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <select id="rawDataTargetSheet" class="form-select">
                                                <option value="">Chọn sheet đích...</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row">
                        <div class="col-md-12 text-center">
                            <button id="rawDataImportBtn" class="btn btn-success btn-lg me-3">
                                <i class="fas fa-play"></i> Bắt Đầu Import
                            </button>
                            <button id="rawDataProcessBtn" class="btn btn-warning btn-lg">
                                <i class="fas fa-cogs"></i> Xử Lý Dữ Liệu
                            </button>
                        </div>
                    </div>

                    <!-- Progress Section -->
                    <div id="rawDataProgress" class="mt-4" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Tiến Trình Xử Lý</h6>
                            </div>
                            <div class="card-body">
                                <div class="progress mb-3">
                                    <div id="rawDataProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%">0%</div>
                                </div>
                                <div id="rawDataStatus" class="text-muted">Đang chuẩn bị...</div>
                                <div id="rawDataLog" class="mt-3" style="max-height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px;">
                                    <!-- Log messages sẽ được thêm ở đây -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End Data Handler Tab -->

            <!-- Data Scraping Tab -->
            <div class="tab-pane fade" id="data-scraping" role="tabpanel">
                <div class="app-step">
                    <h4 class="mb-4"><i class="fas fa-download"></i> Data Scraping Tool</h4>

                    <!-- Step 1: Nhập thông tin và ID -->
        <div id="step1" class="app-step">
            <div class="row mb-3">
                <div class="col-md-3">
                    <label class="form-label">Nơi lưu file:</label>
                </div>
                <div class="col-md-9">
                    <div class="input-group">
                        <input type="text" id="output_path" class="form-control" value="" placeholder="Chọn hoặc nhập đường dẫn thư mục để lưu file">
                        <button id="browse_btn" class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-folder-open"></i> Browse
                        </button>
                    </div>
                    <small class="form-text text-muted">Để trống sẽ lưu vào thư mục mặc định của ứng dụng</small>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-3">
                    <label class="form-label">Tên file Excel:</label>
                </div>
                <div class="col-md-6">
                    <input type="text" id="filename" class="form-control" value="Scraped.xlsx">
                </div>
                <div class="col-md-3">
                    <button id="login_info_btn" class="btn btn-secondary">
                        <i class="fas fa-key"></i> Thông tin đăng nhập
                    </button>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-3">
                    <label class="form-label">Chuyển CSV sang Excel:</label>
                </div>
                <div class="col-md-9">
                    <button id="convert_csv_btn" class="btn btn-secondary">
                        <i class="fas fa-sync-alt"></i> Convert CSV
                    </button>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-12">
                    <label class="form-label">Danh sách ID shop:</label>
                    <textarea id="shop_ids" class="form-control" rows="10" placeholder="Nhập ID shop ở đây hoặc dán từ clipboard"></textarea>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col text-center">
                    <button id="paste_btn" class="btn btn-primary me-3">
                        <i class="fas fa-clipboard"></i> Nhập từ Clipboard
                    </button>
                    <button id="process_btn" class="btn btn-success">
                        <i class="fas fa-play"></i> Xử lý dữ liệu
                    </button>
                </div>
            </div>
        </div>

        <!-- Step 2: Hiển thị log và tiến trình xử lý -->
        <div id="step2" class="app-step" style="display: none;">
            <div class="row mb-3">
                <div class="col text-center">
                    <div class="d-flex justify-content-center align-items-center mb-3">
                        <div class="me-3">
                            <span id="timer_label" class="fw-bold">⏱️ Thời gian: 00:00:00</span>
                        </div>
                        <div class="flex-grow-1" style="max-width: 60%;">
                            <div class="progress" style="height: 25px;">
                                <div id="progress_bar" class="progress-bar bg-success" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                            </div>
                        </div>
                    </div>

                    <h5 id="shop_counter" class="mb-4">💻 Hoàn thành: 0/0</h5>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col">
                    <ul class="nav nav-tabs" id="myTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="progress-tab" data-bs-toggle="tab" data-bs-target="#progress-tab-pane" type="button" role="tab" aria-controls="progress-tab-pane" aria-selected="true">Tiến trình theo Shop</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="log-tab" data-bs-toggle="tab" data-bs-target="#log-tab-pane" type="button" role="tab" aria-controls="log-tab-pane" aria-selected="false">Log chi tiết</button>
                        </li>
                    </ul>
                    <div class="tab-content" id="myTabContent">
                        <div class="tab-pane fade show active" id="progress-tab-pane" role="tabpanel" aria-labelledby="progress-tab" tabindex="0">
                            <div class="table-container">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th style="width: 10%">STT</th>
                                            <th style="width: 20%">ID Shop</th>
                                            <th style="width: 50%">Trạng thái</th>
                                            <th style="width: 20%">Số sản phẩm</th>
                                        </tr>
                                    </thead>
                                    <tbody id="progress_table">
                                        <!-- Dữ liệu sẽ được thêm vào đây bằng JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                            <div class="d-flex justify-content-end mt-3">
                                <h5 id="total_products">📊 Tổng số sản phẩm: 0</h5>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="log-tab-pane" role="tabpanel" aria-labelledby="log-tab" tabindex="0">
                            <div id="log_container" class="log-container">
                                <!-- Log sẽ được thêm vào đây bằng JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col">
                    <button id="back_btn" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Quay lại
                    </button>
                    <div id="file_result" class="alert alert-success mt-3" style="display: none;">
                        <span id="file_result_text"></span>
                        <a href="#" id="file_result_link" class="ms-2">Mở thư mục chứa file</a>
                    </div>
                </div>
                    </div>
                </div>
            </div>
            <!-- End Data Scraping Tab -->

            <!-- More Tools Tab -->
            <div class="tab-pane fade" id="more-tools" role="tabpanel">
                <div class="app-step">
                    <h4 class="mb-4"><i class="fas fa-plus"></i> More Tools</h4>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-image fa-3x mb-3 text-primary"></i>
                                    <h5 class="card-title">Image Processing</h5>
                                    <p class="card-text">Xử lý và tối ưu hóa hình ảnh</p>
                                    <button class="btn btn-primary" disabled>Coming Soon</button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-robot fa-3x mb-3 text-success"></i>
                                    <h5 class="card-title">AI Classification</h5>
                                    <p class="card-text">Phân loại dữ liệu bằng AI</p>
                                    <button class="btn btn-success" disabled>Coming Soon</button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-chart-bar fa-3x mb-3 text-warning"></i>
                                    <h5 class="card-title">Data Analytics</h5>
                                    <p class="card-text">Phân tích và báo cáo dữ liệu</p>
                                    <button class="btn btn-warning" disabled>Coming Soon</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End More Tools Tab -->

        </div>
        <!-- End Tab Content -->

        <!-- Modal đăng nhập -->
        <div class="modal fade" id="loginModal" tabindex="-1" aria-labelledby="loginModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="loginModalLabel">Thông tin đăng nhập</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="username" class="form-label">Tên đăng nhập:</label>
                            <input type="text" class="form-control" id="username">
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Mật khẩu:</label>
                            <input type="password" class="form-control" id="password">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                        <button type="button" class="btn btn-primary" id="save_login_btn">Lưu</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal upload CSV -->
        <div class="modal fade" id="csvModal" tabindex="-1" aria-labelledby="csvModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="csvModalLabel">Convert CSV to Excel</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="csv_file" class="form-label">Chọn file CSV:</label>
                            <input type="file" class="form-control" id="csv_file" accept=".csv">
                        </div>
                        <div id="csv_progress" class="progress" style="display: none; height: 25px;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
                        </div>
                        <div id="csv_result" class="alert mt-3" style="display: none;"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                        <button type="button" class="btn btn-primary" id="upload_csv_btn">Convert</button>
                        <a href="#" class="btn btn-success" id="download_csv_result" style="display: none;">Tải xuống kết quả</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal browse thư mục -->
        <div class="modal fade" id="folderBrowserModal" tabindex="-1" aria-labelledby="folderBrowserModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="folderBrowserModalLabel">Chọn thư mục lưu file</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">Đường dẫn hiện tại:</label>
                            <div class="input-group">
                                <input type="text" id="current_path" class="form-control" readonly>
                                <button id="refresh_folder_btn" class="btn btn-outline-secondary" type="button">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                                <button id="create_folder_btn" class="btn btn-outline-success" type="button">
                                    <i class="fas fa-folder-plus"></i> Tạo thư mục
                                </button>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Thư mục thông dụng:</label>
                            <div class="d-flex flex-wrap gap-2">
                                <button class="btn btn-sm btn-outline-primary" data-folder="desktop">
                                    <i class="fas fa-desktop"></i> Desktop
                                </button>
                                <button class="btn btn-sm btn-outline-primary" data-folder="documents">
                                    <i class="fas fa-file-alt"></i> Documents
                                </button>
                                <button class="btn btn-sm btn-outline-primary" data-folder="downloads">
                                    <i class="fas fa-download"></i> Downloads
                                </button>
                                <button class="btn btn-sm btn-outline-primary" data-folder="temp">
                                    <i class="fas fa-folder"></i> Temp
                                </button>
                            </div>
                        </div>

                        <div class="folder-browser" style="height: 300px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 0.375rem; padding: 0.5rem;">
                            <div id="folder_loading" class="text-center p-3" style="display: none;">
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <span class="ms-2">Đang tải...</span>
                            </div>
                            <div id="folder_list">
                                <!-- Danh sách thư mục sẽ được load ở đây -->
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                        <button type="button" class="btn btn-primary" id="select_folder_btn">Chọn thư mục này</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>