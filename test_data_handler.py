#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script để kiểm tra chức năng Data Handler Core
"""

import sys
import os

# Thêm thư mục hiện tại vào Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from data_handler_core import DataHandlerCore
    print("✅ Import DataHandlerCore thành công")
except ImportError as e:
    print(f"❌ Lỗi import DataHandlerCore: {e}")
    sys.exit(1)

def test_extract_spreadsheet_id():
    """Test chức năng extract spreadsheet ID"""
    print("\n🔍 Test extract_spreadsheet_id:")
    
    data_handler = DataHandlerCore()
    
    test_cases = [
        # URL đầy đủ
        "https://docs.google.com/spreadsheets/d/1cqYTU7_CSLzmoR4OdvHSMNw2IlYkqpX_cV_MIv8/edit#gid=0",
        # URL ngắn
        "https://docs.google.com/spreadsheets/d/1cqYTU7_CSLzmoR4OdvHSMNw2IlYkqpX_cV_MIv8",
        # ID thuần
        "1cqYTU7_CSLzmoR4OdvHSMNw2IlYkqpX_cV_MIv8",
        # URL với /e/
        "https://docs.google.com/spreadsheets/d/e/1cqYTU7_CSLzmoR4OdvHSMNw2IlYkqpX_cV_MIv8/pubhtml"
    ]
    
    for i, test_url in enumerate(test_cases, 1):
        try:
            result = data_handler.extract_spreadsheet_id(test_url)
            print(f"  Test {i}: '{test_url[:50]}...' → '{result}'")
        except Exception as e:
            print(f"  Test {i}: Lỗi - {e}")

def test_basic_functions():
    """Test các chức năng cơ bản"""
    print("\n🧪 Test basic functions:")
    
    try:
        data_handler = DataHandlerCore()
        
        # Test normalize_number_format
        test_numbers = ["219.000", "219,000.40", "219.000,40", "123.45", "123,45", "123"]
        print("  Test normalize_number_format:")
        for num in test_numbers:
            result = data_handler.normalize_number_format(num)
            print(f"    '{num}' → '{result}'")
        
        # Test remove_vietnamese_accents
        test_texts = ["Tên sản phẩm", "Giá hiện tại", "Thương hiệu", "Chuyên mục"]
        print("  Test remove_vietnamese_accents:")
        for text in test_texts:
            result = data_handler.remove_vietnamese_accents(text)
            print(f"    '{text}' → '{result}'")
            
        # Test column conversion
        print("  Test column conversion:")
        for i in range(5):
            col_letter = data_handler.index_to_column_letter(i)
            col_index = data_handler.column_to_index(col_letter)
            print(f"    Index {i} → '{col_letter}' → Index {col_index}")
            
    except Exception as e:
        print(f"  ❌ Lỗi: {e}")

if __name__ == "__main__":
    print("🚀 Bắt đầu test Data Handler Core")
    
    test_extract_spreadsheet_id()
    test_basic_functions()
    
    print("\n✅ Hoàn thành test")
